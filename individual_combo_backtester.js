// ============================================================================
// INDIVIDUAL COMBO BACKTESTER - VALIDATED TRADING EDGE
// ============================================================================
// Tests single SL/TP combinations with 1-second data validation
// Uses pre-built 1-minute candles for signal generation
// Validates all entries through 1-second tick data for accurate SL/TP hits
//
// ✅ TRADING EDGE - CORRECTED SPECIFICATION:
// • 3-Candle Pattern: Green-Red-Green (bullish) / Red-Green-Red (bearish) - NO BODY VALIDATION
// • MA Confluence: Price > WMA50 > WMA200 (bullish) / Price < WMA50 < WMA200 (bearish)
// • RSI Conditions: RSI > 60 (bullish) / RSI < 40 (bearish) - NO RSI-MA
// • Trading Hours: 7am-3pm CST/CDT (UTC 13-20 standard, 12-19 daylight)
// • Indicators: RSI(14), WMA(50), WMA(200) calculated on 24-hour data
// • Slippage: Entry +0.25, SL +0.5, TP +0.0 points
// • Contract Value: $6 per point for MNQ micro contracts
// ============================================================================

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ============================================================================
// CONFIGURATION
// ============================================================================

const CONFIG = {
    // Data source configuration
    CANDLES_DIR: process.env.CANDLES_DIR || 'D:\\backtest-output\\1min_candles_built_from_1sec',
    OHLCV_1SEC_DIR: process.env.OHLCV_1SEC_DIR || 'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
    OUT_DIR: process.env.OUT_DIR || 'D:\\backtest-output\\individual_combo_results',
    
    // Date range - Past 1 year (Aug 2024 - Aug 2025)
    START_DATE: '2024-08-30',
    END_DATE: '2025-08-29',
    
    // Single combo configuration (set via command line)
    SL_POINTS: parseInt(process.env.SL_POINTS) || 6,
    TP_POINTS: parseInt(process.env.TP_POINTS) || 8,
    
    // Trading configuration
    SLIPPAGE: {
        ENTRY: 0.25,
        SL: 0.5,
        TP: 0.0
    },
    
    // Time filtering - 7am-3pm CST/CDT
    TRADING_HOURS: {
        START_HOUR_STANDARD: 13, // 7am CST
        END_HOUR_STANDARD: 21,   // 3pm CST
        START_HOUR_DAYLIGHT: 12, // 7am CDT
        END_HOUR_DAYLIGHT: 20    // 3pm CDT
    },
    
    // Contract specifications
    SYMBOL: 'MNQ',
    CONTRACT_VALUE: 6, // $6 per point for MNQ micro contracts
    POSITION_SIZE: 3,  // 3 micro contracts
    
    // Technical indicators
    RSI_PERIOD: 14,
    WMA_FAST: 50,
    WMA_SLOW: 200,
    
    // Logging
    LOG_LEVEL: process.env.LOG_LEVEL || 'INFO',
    LOG_TRADES: true,
    LOG_SIGNALS: false
};

// ============================================================================
// LOGGING UTILITIES
// ============================================================================

function log(message, level = 'INFO') {
    const levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    if (levels[level] >= levels[CONFIG.LOG_LEVEL]) {
        const timestamp = new Date().toISOString();
        console.log(`${timestamp} [${level}] ${message}`);
    }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function isDST(date) {
    const year = date.getFullYear();
    const march = new Date(year, 2, 1);
    const november = new Date(year, 10, 1);
    const dstStart = new Date(year, 2, 14 - march.getDay());
    const dstEnd = new Date(year, 10, 7 - november.getDay());
    return date >= dstStart && date < dstEnd;
}

function isWithinTradingHours(timestamp) {
    const date = new Date(timestamp);
    const hour = date.getUTCHours();
    
    if (isDST(date)) {
        return hour >= CONFIG.TRADING_HOURS.START_HOUR_DAYLIGHT && 
               hour < CONFIG.TRADING_HOURS.END_HOUR_DAYLIGHT;
    } else {
        return hour >= CONFIG.TRADING_HOURS.START_HOUR_STANDARD && 
               hour < CONFIG.TRADING_HOURS.END_HOUR_STANDARD;
    }
}

function isWeekend(date) {
    const day = date.getDay();
    return day === 0 || day === 6;
}

function getFrontMonthContract(date) {
    // CME E-mini NASDAQ rollover: Monday prior to 3rd Friday of contract month
    // Contract months: H (March), M (June), U (September), Z (December)

    const year = date.getFullYear();
    const month = date.getMonth(); // 0-based

    // Define contract months and their codes
    const contractMonths = [
        { month: 2, code: 'H' },  // March
        { month: 5, code: 'M' },  // June
        { month: 8, code: 'U' },  // September
        { month: 11, code: 'Z' }  // December
    ];

    // Find current and next contract months
    let currentContract = null;
    let nextContract = null;

    for (let i = 0; i < contractMonths.length; i++) {
        const contract = contractMonths[i];
        const nextContractIndex = (i + 1) % contractMonths.length;
        const nextContractYear = nextContractIndex === 0 ? year + 1 : year;

        if (month <= contract.month) {
            currentContract = `${CONFIG.SYMBOL}${contract.code}${year.toString().slice(-1)}`;
            nextContract = `${CONFIG.SYMBOL}${contractMonths[nextContractIndex].code}${nextContractYear.toString().slice(-1)}`;
            break;
        }
    }

    // If we're past December, roll to next year's March
    if (!currentContract) {
        currentContract = `${CONFIG.SYMBOL}H${(year + 1).toString().slice(-1)}`;
        nextContract = `${CONFIG.SYMBOL}M${(year + 1).toString().slice(-1)}`;
    }

    // Check if we're past rollover date for current contract
    const contractMonth = currentContract.includes('H') ? 2 :
                         currentContract.includes('M') ? 5 :
                         currentContract.includes('U') ? 8 : 11;

    // Third Friday of contract month
    const thirdFriday = getThirdFriday(year, contractMonth);
    // Monday prior to third Friday
    const rolloverDate = new Date(thirdFriday);
    rolloverDate.setDate(thirdFriday.getDate() - 4); // Go back to Monday

    // If current date is past rollover, use next contract
    if (date >= rolloverDate) {
        return nextContract;
    }

    return currentContract;
}

function getThirdFriday(year, month) {
    // Find third Friday of given month
    const firstDay = new Date(year, month, 1);
    const firstFriday = new Date(firstDay);

    // Find first Friday
    const daysToFriday = (5 - firstDay.getDay() + 7) % 7;
    firstFriday.setDate(1 + daysToFriday);

    // Third Friday is 14 days later
    const thirdFriday = new Date(firstFriday);
    thirdFriday.setDate(firstFriday.getDate() + 14);

    return thirdFriday;
}

function formatDate(date) {
    return date.toISOString().split('T')[0].replace(/-/g, '');
}

// ============================================================================
// DATA LOADING FUNCTIONS
// ============================================================================

function loadPrebuiltCandles(date) {
    const dateStr = formatDate(date);
    const candleFile = path.join(CONFIG.CANDLES_DIR, `${dateStr}_1min_candles.csv`);
    
    if (!fs.existsSync(candleFile)) {
        return [];
    }
    
    try {
        const csvData = fs.readFileSync(candleFile, 'utf8');
        const lines = csvData.trim().split('\n');
        const candles = [];
        
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            const parts = line.split(',');
            if (parts.length < 7) continue;
            
            const candle = {
                symbol: parts[0],
                timestamp: new Date(parts[1]),
                open: parseFloat(parts[2]),
                high: parseFloat(parts[3]),
                low: parseFloat(parts[4]),
                close: parseFloat(parts[5]),
                volume: parseInt(parts[6])
            };
            
            if (isWithinTradingHours(candle.timestamp)) {
                candles.push(candle);
            }
        }
        
        return candles;
        
    } catch (error) {
        log(`Error loading candles for ${dateStr}: ${error.message}`, 'ERROR');
        return [];
    }
}

function load1SecondData(date) {
    const dateStr = formatDate(date);
    const fileName = `glbx-mdp3-${dateStr}.ohlcv-1s.csv.zst`;
    const filePath = path.join(CONFIG.OHLCV_1SEC_DIR, fileName);

    if (!fs.existsSync(filePath)) {
        return [];
    }

    try {
        const csvData = execSync(`zstd -d -c "${filePath}"`, {
            encoding: 'utf8',
            maxBuffer: 1024 * 1024 * 100 // 100MB buffer
        });
        const lines = csvData.trim().split('\n');

        // CRITICAL FIX: Use proper CME rollover logic instead of volume-based selection
        const primaryContract = getFrontMonthContract(date);

        if (!primaryContract) {
            log(`No valid front-month contract found for ${dateStr}`, 'WARN');
            return [];
        }

        log(`Using front-month contract: ${primaryContract} for ${dateStr}`, 'INFO');

        // Second pass: Load only primary contract data with price validation
        const data = [];
        let lastPrice = null;

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const parts = line.split(',');
            if (parts.length < 10) continue;

            const symbol = parts[9];
            if (symbol !== primaryContract) continue; // ONLY use primary contract

            const timestamp = new Date(parts[0]);
            if (!isWithinTradingHours(timestamp)) continue;

            const tick = {
                symbol,
                timestamp,
                open: parseFloat(parts[4]),
                high: parseFloat(parts[5]),
                low: parseFloat(parts[6]),
                close: parseFloat(parts[7]),
                volume: parseInt(parts[8]) || 0
            };

            if (isNaN(tick.open) || isNaN(tick.high) || isNaN(tick.low) || isNaN(tick.close)) continue;

            // CRITICAL: Price sanity check to reject corrupted data
            if (lastPrice !== null) {
                const priceMove = Math.abs(tick.close - lastPrice);
                if (priceMove > 10) { // Reject moves >10 points per second
                    log(`Rejected corrupted tick: ${lastPrice} → ${tick.close} (${priceMove.toFixed(2)} points)`, 'WARN');
                    continue;
                }
            }

            data.push(tick);
            lastPrice = tick.close;
        }

        log(`Loaded ${data.length} clean 1-second ticks for ${primaryContract}`, 'INFO');
        return data;

    } catch (error) {
        log(`Error loading 1-second data for ${dateStr}: ${error.message}`, 'ERROR');
        return [];
    }
}

// ============================================================================
// TECHNICAL INDICATORS
// ============================================================================

function calculateWMA(prices, period) {
    if (prices.length < period) return null;
    
    const weights = [];
    let weightSum = 0;
    for (let i = 1; i <= period; i++) {
        weights.push(i);
        weightSum += i;
    }
    
    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += prices[prices.length - period + i] * weights[i];
    }
    
    return sum / weightSum;
}

function calculateRSI(prices, period) {
    if (prices.length < period + 1) return null;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i <= period; i++) {
        const change = prices[i] - prices[i - 1];
        if (change > 0) {
            gains += change;
        } else {
            losses -= change;
        }
    }
    
    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    for (let i = period + 1; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        const gain = change > 0 ? change : 0;
        const loss = change < 0 ? -change : 0;
        
        avgGain = (avgGain * (period - 1) + gain) / period;
        avgLoss = (avgLoss * (period - 1) + loss) / period;
    }
    
    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

function calculateIndicators(candles) {
    if (candles.length < Math.max(CONFIG.RSI_PERIOD, CONFIG.WMA_SLOW) + 1) {
        return null;
    }
    
    const closes = candles.map(c => c.close);
    
    return {
        rsi: calculateRSI(closes, CONFIG.RSI_PERIOD),
        wma50: calculateWMA(closes, CONFIG.WMA_FAST),
        wma200: calculateWMA(closes, CONFIG.WMA_SLOW)
    };
}

// ============================================================================
// SIGNAL DETECTION
// ============================================================================

function detect3CandlePattern(candles) {
    if (candles.length < 3) return null;
    
    const [c1, c2, c3] = candles.slice(-3);
    
    const isGreenRedGreen = (c1.close > c1.open) && (c2.close < c2.open) && (c3.close > c3.open);
    const isRedGreenRed = (c1.close < c1.open) && (c2.close > c2.open) && (c3.close < c3.open);
    
    if (isGreenRedGreen) {
        return 'BULLISH';
    } else if (isRedGreenRed) {
        return 'BEARISH';
    }
    
    return null;
}

function detectTradingSignal(candles) {
    if (candles.length < Math.max(CONFIG.RSI_PERIOD, CONFIG.WMA_SLOW) + 3) {
        return null;
    }
    
    const indicators = calculateIndicators(candles);
    if (!indicators) return null;
    
    const pattern = detect3CandlePattern(candles);
    if (!pattern) return null;
    
    const currentPrice = candles[candles.length - 1].close;
    const { rsi, wma50, wma200 } = indicators;
    
    if (pattern === 'BULLISH' && 
        currentPrice > wma50 && 
        wma50 > wma200 && 
        rsi > 60) {
        return {
            type: 'LONG',
            price: currentPrice,
            timestamp: candles[candles.length - 1].timestamp,
            rsi,
            wma50,
            wma200
        };
    }
    
    if (pattern === 'BEARISH' && 
        currentPrice < wma50 && 
        wma50 < wma200 && 
        rsi < 40) {
        return {
            type: 'SHORT',
            price: currentPrice,
            timestamp: candles[candles.length - 1].timestamp,
            rsi,
            wma50,
            wma200
        };
    }
    
    return null;
}

// ============================================================================
// TRADE VALIDATION
// ============================================================================

function validateTradeExit(trade, oneSecondData) {
    const entryPrice = trade.price + (trade.type === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY);
    const slPrice = trade.type === 'LONG' 
        ? entryPrice - CONFIG.SL_POINTS - CONFIG.SLIPPAGE.SL
        : entryPrice + CONFIG.SL_POINTS + CONFIG.SLIPPAGE.SL;
    const tpPrice = trade.type === 'LONG' 
        ? entryPrice + CONFIG.TP_POINTS + CONFIG.SLIPPAGE.TP
        : entryPrice - CONFIG.TP_POINTS - CONFIG.SLIPPAGE.TP;
    
    const entryTime = trade.timestamp.getTime();
    const relevantTicks = oneSecondData.filter(tick => tick.timestamp.getTime() > entryTime);
    
    for (const tick of relevantTicks) {
        // Check SL hit first (SL has priority)
        if (trade.type === 'LONG' && tick.low <= slPrice) {
            return {
                exitType: 'SL',
                exitPrice: slPrice,
                exitTime: tick.timestamp,
                pnl: (slPrice - entryPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        if (trade.type === 'SHORT' && tick.high >= slPrice) {
            return {
                exitType: 'SL',
                exitPrice: slPrice,
                exitTime: tick.timestamp,
                pnl: (entryPrice - slPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        
        // Check TP hit
        if (trade.type === 'LONG' && tick.high >= tpPrice) {
            return {
                exitType: 'TP',
                exitPrice: tpPrice,
                exitTime: tick.timestamp,
                pnl: (tpPrice - entryPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        if (trade.type === 'SHORT' && tick.low <= tpPrice) {
            return {
                exitType: 'TP',
                exitPrice: tpPrice,
                exitTime: tick.timestamp,
                pnl: (entryPrice - tpPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
    }
    
    return null;
}

// ============================================================================
// MAIN EXECUTION
// ============================================================================

async function main() {
    log(`🚀 Individual Combo Backtester Starting: SL${CONFIG.SL_POINTS}_TP${CONFIG.TP_POINTS}`);
    log(`📊 Date Range: ${CONFIG.START_DATE} to ${CONFIG.END_DATE}`);
    
    if (!fs.existsSync(CONFIG.OUT_DIR)) {
        fs.mkdirSync(CONFIG.OUT_DIR, { recursive: true });
    }
    
    const results = {
        slPoints: CONFIG.SL_POINTS,
        tpPoints: CONFIG.TP_POINTS,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalPnL: 0,
        maxDrawdown: 0,
        trades: []
    };
    
    const startDate = new Date(CONFIG.START_DATE);
    const endDate = new Date(CONFIG.END_DATE);
    let currentDate = new Date(startDate);
    let runningPnL = 0;
    let peakPnL = 0;
    let processedDays = 0;
    
    while (currentDate <= endDate) {
        if (isWeekend(currentDate)) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        const candles = loadPrebuiltCandles(currentDate);
        if (candles.length === 0) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        const oneSecondData = load1SecondData(currentDate);
        if (oneSecondData.length === 0) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        processedDays++;
        if (processedDays % 100 === 0) {
            log(`📊 Processed ${processedDays} days, ${results.totalTrades} trades found`);
        }
        
        const candleBuffer = [];
        
        for (const candle of candles) {
            candleBuffer.push(candle);
            
            if (candleBuffer.length > CONFIG.WMA_SLOW + 50) {
                candleBuffer.shift();
            }
            
            if (candleBuffer.length >= CONFIG.WMA_SLOW + 3) {
                const signal = detectTradingSignal(candleBuffer);
                
                if (signal) {
                    const tradeResult = validateTradeExit(signal, oneSecondData);
                    
                    if (tradeResult) {
                        results.totalTrades++;
                        results.totalPnL += tradeResult.pnl;
                        runningPnL += tradeResult.pnl;
                        
                        if (tradeResult.pnl > 0) {
                            results.winningTrades++;
                        } else {
                            results.losingTrades++;
                        }
                        
                        if (runningPnL > peakPnL) {
                            peakPnL = runningPnL;
                        }
                        const currentDrawdown = peakPnL - runningPnL;
                        if (currentDrawdown > results.maxDrawdown) {
                            results.maxDrawdown = currentDrawdown;
                        }
                        
                        const trade = {
                            ...signal,
                            ...tradeResult,
                            slPoints: CONFIG.SL_POINTS,
                            tpPoints: CONFIG.TP_POINTS,
                            entryPrice: signal.price + (signal.type === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY)
                        };
                        
                        results.trades.push(trade);
                        
                        if (CONFIG.LOG_TRADES) {
                            log(`📈 ${trade.type} ${trade.exitType}: Entry=${trade.entryPrice.toFixed(2)}, Exit=${trade.exitPrice.toFixed(2)}, PnL=$${trade.pnl.toFixed(2)}`);
                        }
                    }
                }
            }
        }
        
        currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Calculate final metrics
    results.winRate = results.totalTrades > 0 ? (results.winningTrades / results.totalTrades * 100) : 0;
    results.avgWin = results.winningTrades > 0 ? results.trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0) / results.winningTrades : 0;
    results.avgLoss = results.losingTrades > 0 ? results.trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0) / results.losingTrades : 0;
    results.profitFactor = results.avgLoss !== 0 ? Math.abs(results.avgWin * results.winningTrades / (results.avgLoss * results.losingTrades)) : 0;
    
    // Save results
    const outputFile = path.join(CONFIG.OUT_DIR, `SL${CONFIG.SL_POINTS}_TP${CONFIG.TP_POINTS}_results.json`);
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    
    log(`✅ Backtest Complete: SL${CONFIG.SL_POINTS}_TP${CONFIG.TP_POINTS}`);
    log(`📊 ${results.totalTrades} trades, ${results.winRate.toFixed(1)}% win rate, $${results.totalPnL.toFixed(2)} total PnL`);
    log(`📊 Max Drawdown: $${results.maxDrawdown.toFixed(2)}, Profit Factor: ${results.profitFactor.toFixed(2)}`);
    log(`📊 Results saved to: ${outputFile}`);
}

if (require.main === module) {
    main().catch(error => {
        log(`❌ Fatal error: ${error.message}`, 'ERROR');
        console.error(error);
        process.exit(1);
    });
}

module.exports = { CONFIG, main };
