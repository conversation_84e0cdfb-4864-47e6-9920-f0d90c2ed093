// ============================================================================
// COMPREHENSIVE GRID BACKTESTER - VALIDATED TRADING EDGE
// ============================================================================
// Tests all SL/TP combinations with 1-second data validation
// SL Range: 4, 5, 6, 7, 8, 9, 10, 12
// TP Range: 6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 25
// Total: 96 combinations (8 SL × 12 TP)
//
// ✅ TRADING EDGE - CORRECTED SPECIFICATION:
// • 3-Candle Pattern: Green-Red-Green (bullish) / Red-Green-Red (bearish)
// • MA Confluence: Price > WMA50 > WMA200 (bullish) / Price < WMA50 < WMA200 (bearish)
// • RSI Conditions: RSI > 60 (bullish) / RSI < 40 (bearish)
// • Trading Hours: 7am-3pm CST/CDT
// • Date Range: Past 1 year (Aug 2024 - Aug 2025)
// ============================================================================

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ============================================================================
// CONFIGURATION
// ============================================================================

const CONFIG = {
    // Data source configuration
    CANDLES_DIR: 'D:\\backtest-output\\1min_candles_built_from_1sec',
    OHLCV_1SEC_DIR: 'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
    OUT_DIR: 'D:\\backtest-output\\comprehensive_grid_results',
    
    // Date range - Past 1 year
    START_DATE: '2024-08-30',
    END_DATE: '2025-08-29',
    
    // Grid configuration
    SL_RANGE: [4, 5, 6, 7, 8, 9, 10, 12],
    TP_RANGE: [6, 7, 8, 9, 10, 12, 14, 16, 18, 20, 22, 25],
    
    // Trading configuration
    SLIPPAGE: {
        ENTRY: 0.25,
        SL: 0.5,
        TP: 0.0
    },
    
    // Time filtering - 7am-3pm CST/CDT
    TRADING_HOURS: {
        START_HOUR_STANDARD: 13, // 7am CST
        END_HOUR_STANDARD: 21,   // 3pm CST
        START_HOUR_DAYLIGHT: 12, // 7am CDT
        END_HOUR_DAYLIGHT: 20    // 3pm CDT
    },
    
    // Contract specifications
    SYMBOL: 'MNQ',
    CONTRACT_VALUE: 6, // $6 per point for MNQ micro contracts
    POSITION_SIZE: 3,  // 3 micro contracts
    
    // Technical indicators
    RSI_PERIOD: 14,
    WMA_FAST: 50,
    WMA_SLOW: 200,
    
    // Logging
    LOG_LEVEL: 'INFO',
    LOG_TRADES: false, // Disable individual trade logging for grid test
    LOG_PROGRESS: true
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function isDST(date) {
    const year = date.getFullYear();
    const march = new Date(year, 2, 1);
    const november = new Date(year, 10, 1);
    const dstStart = new Date(year, 2, 14 - march.getDay());
    const dstEnd = new Date(year, 10, 7 - november.getDay());
    return date >= dstStart && date < dstEnd;
}

function isWithinTradingHours(timestamp) {
    const date = new Date(timestamp);
    const hour = date.getUTCHours();

    if (isDST(date)) {
        return hour >= CONFIG.TRADING_HOURS.START_HOUR_DAYLIGHT &&
               hour < CONFIG.TRADING_HOURS.END_HOUR_DAYLIGHT;
    } else {
        return hour >= CONFIG.TRADING_HOURS.START_HOUR_STANDARD &&
               hour < CONFIG.TRADING_HOURS.END_HOUR_STANDARD;
    }
}

function isWeekend(date) {
    const day = date.getDay();
    return day === 0 || day === 6;
}

function formatDate(date) {
    return date.toISOString().split('T')[0].replace(/-/g, '');
}

// ============================================================================
// LOGGING UTILITIES
// ============================================================================

function log(message, level = 'INFO') {
    const levels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
    if (levels[level] >= levels[CONFIG.LOG_LEVEL]) {
        const timestamp = new Date().toISOString();
        console.log(`${timestamp} [${level}] ${message}`);
    }
}

// ============================================================================
// DATA LOADING FUNCTIONS
// ============================================================================

function loadPrebuiltCandles(date) {
    const dateStr = formatDate(date);
    const candleFile = path.join(CONFIG.CANDLES_DIR, `${dateStr}_1min_candles.csv`);

    if (!fs.existsSync(candleFile)) {
        return [];
    }

    try {
        const csvData = fs.readFileSync(candleFile, 'utf8');
        const lines = csvData.trim().split('\n');
        const candles = [];

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const parts = line.split(',');
            if (parts.length < 7) continue;

            const candle = {
                symbol: parts[0],
                timestamp: new Date(parts[1]),
                open: parseFloat(parts[2]),
                high: parseFloat(parts[3]),
                low: parseFloat(parts[4]),
                close: parseFloat(parts[5]),
                volume: parseInt(parts[6])
            };

            if (isWithinTradingHours(candle.timestamp)) {
                candles.push(candle);
            }
        }

        return candles;

    } catch (error) {
        log(`Error loading candles for ${dateStr}: ${error.message}`, 'ERROR');
        return [];
    }
}

function load1SecondData(date) {
    const dateStr = formatDate(date);
    const fileName = `glbx-mdp3-${dateStr}.ohlcv-1s.csv.zst`;
    const filePath = path.join(CONFIG.OHLCV_1SEC_DIR, fileName);

    if (!fs.existsSync(filePath)) {
        return [];
    }

    try {
        const csvData = execSync(`zstd -d -c "${filePath}"`, {
            encoding: 'utf8',
            maxBuffer: 1024 * 1024 * 100 // 100MB buffer
        });
        const lines = csvData.trim().split('\n');

        // CRITICAL FIX: First pass to identify primary contract and volume
        const contractVolumes = {};
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const parts = line.split(',');
            if (parts.length < 10) continue;

            const symbol = parts[9];
            if (!symbol.startsWith(CONFIG.SYMBOL) || symbol.includes('-')) continue;

            const timestamp = new Date(parts[0]);
            if (!isWithinTradingHours(timestamp)) continue;

            const volume = parseInt(parts[8]) || 0;
            contractVolumes[symbol] = (contractVolumes[symbol] || 0) + volume;
        }

        // Find the primary contract (highest volume)
        const primaryContract = Object.keys(contractVolumes).reduce((a, b) =>
            contractVolumes[a] > contractVolumes[b] ? a : b, '');

        if (!primaryContract) {
            return [];
        }

        // Second pass: Load only primary contract data with price validation
        const data = [];
        let lastPrice = null;

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const parts = line.split(',');
            if (parts.length < 10) continue;

            const symbol = parts[9];
            if (symbol !== primaryContract) continue; // ONLY use primary contract

            const timestamp = new Date(parts[0]);
            if (!isWithinTradingHours(timestamp)) continue;

            const tick = {
                symbol,
                timestamp,
                open: parseFloat(parts[4]),
                high: parseFloat(parts[5]),
                low: parseFloat(parts[6]),
                close: parseFloat(parts[7]),
                volume: parseInt(parts[8]) || 0
            };

            if (isNaN(tick.open) || isNaN(tick.high) || isNaN(tick.low) || isNaN(tick.close)) continue;

            // CRITICAL: Price sanity check to reject corrupted data
            if (lastPrice !== null) {
                const priceMove = Math.abs(tick.close - lastPrice);
                if (priceMove > 10) { // Reject moves >10 points per second
                    continue;
                }
            }

            data.push(tick);
            lastPrice = tick.close;
        }

        return data;

    } catch (error) {
        log(`Error loading 1-second data for ${dateStr}: ${error.message}`, 'ERROR');
        return [];
    }
}

// ============================================================================
// TECHNICAL INDICATORS
// ============================================================================

function calculateWMA(prices, period) {
    if (prices.length < period) return null;

    const weights = [];
    let weightSum = 0;
    for (let i = 1; i <= period; i++) {
        weights.push(i);
        weightSum += i;
    }

    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += prices[prices.length - period + i] * weights[i];
    }

    return sum / weightSum;
}

function calculateRSI(prices, period) {
    if (prices.length < period + 1) return null;

    let gains = 0;
    let losses = 0;

    for (let i = 1; i <= period; i++) {
        const change = prices[i] - prices[i - 1];
        if (change > 0) {
            gains += change;
        } else {
            losses -= change;
        }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    for (let i = period + 1; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        const gain = change > 0 ? change : 0;
        const loss = change < 0 ? -change : 0;

        avgGain = (avgGain * (period - 1) + gain) / period;
        avgLoss = (avgLoss * (period - 1) + loss) / period;
    }

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

function calculateIndicators(candles) {
    if (candles.length < Math.max(CONFIG.RSI_PERIOD, CONFIG.WMA_SLOW) + 1) {
        return null;
    }

    const closes = candles.map(c => c.close);

    return {
        rsi: calculateRSI(closes, CONFIG.RSI_PERIOD),
        wma50: calculateWMA(closes, CONFIG.WMA_FAST),
        wma200: calculateWMA(closes, CONFIG.WMA_SLOW)
    };
}

// ============================================================================
// SIGNAL DETECTION
// ============================================================================

function detect3CandlePattern(candles) {
    if (candles.length < 3) return null;

    const [c1, c2, c3] = candles.slice(-3);

    const isGreenRedGreen = (c1.close > c1.open) && (c2.close < c2.open) && (c3.close > c3.open);
    const isRedGreenRed = (c1.close < c1.open) && (c2.close > c2.open) && (c3.close < c3.open);

    if (isGreenRedGreen) {
        return 'BULLISH';
    } else if (isRedGreenRed) {
        return 'BEARISH';
    }

    return null;
}

function detectTradingSignal(candles) {
    if (candles.length < Math.max(CONFIG.RSI_PERIOD, CONFIG.WMA_SLOW) + 3) {
        return null;
    }

    const indicators = calculateIndicators(candles);
    if (!indicators) return null;

    const pattern = detect3CandlePattern(candles);
    if (!pattern) return null;

    const currentPrice = candles[candles.length - 1].close;
    const { rsi, wma50, wma200 } = indicators;

    if (pattern === 'BULLISH' &&
        currentPrice > wma50 &&
        wma50 > wma200 &&
        rsi > 60) {
        return {
            type: 'LONG',
            price: currentPrice,
            timestamp: candles[candles.length - 1].timestamp,
            rsi,
            wma50,
            wma200
        };
    }

    if (pattern === 'BEARISH' &&
        currentPrice < wma50 &&
        wma50 < wma200 &&
        rsi < 40) {
        return {
            type: 'SHORT',
            price: currentPrice,
            timestamp: candles[candles.length - 1].timestamp,
            rsi,
            wma50,
            wma200
        };
    }

    return null;
}

// ============================================================================
// GRID BACKTESTING ENGINE
// ============================================================================

async function runSingleCombo(slPoints, tpPoints) {
    const results = {
        slPoints,
        tpPoints,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalPnL: 0,
        maxDrawdown: 0,
        trades: []
    };
    
    const startDate = new Date(CONFIG.START_DATE);
    const endDate = new Date(CONFIG.END_DATE);
    let currentDate = new Date(startDate);
    let runningPnL = 0;
    let peakPnL = 0;
    let processedDays = 0;
    
    while (currentDate <= endDate) {
        if (isWeekend(currentDate)) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        const candles = loadPrebuiltCandles(currentDate);
        if (candles.length === 0) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        const oneSecondData = load1SecondData(currentDate);
        if (oneSecondData.length === 0) {
            currentDate.setDate(currentDate.getDate() + 1);
            continue;
        }
        
        processedDays++;
        
        const candleBuffer = [];
        
        for (const candle of candles) {
            candleBuffer.push(candle);
            
            if (candleBuffer.length > CONFIG.WMA_SLOW + 50) {
                candleBuffer.shift();
            }
            
            if (candleBuffer.length >= CONFIG.WMA_SLOW + 3) {
                const signal = detectTradingSignal(candleBuffer);
                
                if (signal) {
                    const tradeResult = validateTradeExitCustom(signal, oneSecondData, slPoints, tpPoints);
                    
                    if (tradeResult) {
                        results.totalTrades++;
                        results.totalPnL += tradeResult.pnl;
                        runningPnL += tradeResult.pnl;
                        
                        if (tradeResult.pnl > 0) {
                            results.winningTrades++;
                        } else {
                            results.losingTrades++;
                        }
                        
                        if (runningPnL > peakPnL) {
                            peakPnL = runningPnL;
                        }
                        const currentDrawdown = peakPnL - runningPnL;
                        if (currentDrawdown > results.maxDrawdown) {
                            results.maxDrawdown = currentDrawdown;
                        }
                        
                        const trade = {
                            ...signal,
                            ...tradeResult,
                            slPoints,
                            tpPoints,
                            entryPrice: signal.price + (signal.type === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY)
                        };
                        
                        results.trades.push(trade);
                    }
                }
            }
        }
        
        currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Calculate final metrics
    results.winRate = results.totalTrades > 0 ? (results.winningTrades / results.totalTrades * 100) : 0;
    results.avgWin = results.winningTrades > 0 ? results.trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0) / results.winningTrades : 0;
    results.avgLoss = results.losingTrades > 0 ? results.trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0) / results.losingTrades : 0;
    results.profitFactor = results.avgLoss !== 0 ? Math.abs(results.avgWin * results.winningTrades / (results.avgLoss * results.losingTrades)) : 0;
    
    return results;
}

function validateTradeExitCustom(trade, oneSecondData, slPoints, tpPoints) {
    const entryPrice = trade.price + (trade.type === 'LONG' ? CONFIG.SLIPPAGE.ENTRY : -CONFIG.SLIPPAGE.ENTRY);
    const slPrice = trade.type === 'LONG' 
        ? entryPrice - slPoints - CONFIG.SLIPPAGE.SL
        : entryPrice + slPoints + CONFIG.SLIPPAGE.SL;
    const tpPrice = trade.type === 'LONG' 
        ? entryPrice + tpPoints + CONFIG.SLIPPAGE.TP
        : entryPrice - tpPoints - CONFIG.SLIPPAGE.TP;
    
    const entryTime = trade.timestamp.getTime();
    const relevantTicks = oneSecondData.filter(tick => tick.timestamp.getTime() > entryTime);
    
    for (const tick of relevantTicks) {
        // Check SL hit first (SL has priority)
        if (trade.type === 'LONG' && tick.low <= slPrice) {
            return {
                exitType: 'SL',
                exitPrice: slPrice,
                exitTime: tick.timestamp,
                pnl: (slPrice - entryPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        if (trade.type === 'SHORT' && tick.high >= slPrice) {
            return {
                exitType: 'SL',
                exitPrice: slPrice,
                exitTime: tick.timestamp,
                pnl: (entryPrice - slPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        
        // Check TP hit
        if (trade.type === 'LONG' && tick.high >= tpPrice) {
            return {
                exitType: 'TP',
                exitPrice: tpPrice,
                exitTime: tick.timestamp,
                pnl: (tpPrice - entryPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
        if (trade.type === 'SHORT' && tick.low <= tpPrice) {
            return {
                exitType: 'TP',
                exitPrice: tpPrice,
                exitTime: tick.timestamp,
                pnl: (entryPrice - tpPrice) * CONFIG.POSITION_SIZE * CONFIG.CONTRACT_VALUE
            };
        }
    }
    
    return null;
}

// ============================================================================
// MAIN EXECUTION
// ============================================================================

async function main() {
    log('🚀 Comprehensive Grid Backtester Starting...');
    log(`📊 SL Range: [${CONFIG.SL_RANGE.join(', ')}]`);
    log(`📊 TP Range: [${CONFIG.TP_RANGE.join(', ')}]`);
    log(`📊 Total Combinations: ${CONFIG.SL_RANGE.length * CONFIG.TP_RANGE.length}`);
    log(`📊 Date Range: ${CONFIG.START_DATE} to ${CONFIG.END_DATE}`);
    log(`📊 Trading Hours: 7am-3pm CST/CDT`);
    
    if (!fs.existsSync(CONFIG.OUT_DIR)) {
        fs.mkdirSync(CONFIG.OUT_DIR, { recursive: true });
    }
    
    const allResults = [];
    let completedCombos = 0;
    const totalCombos = CONFIG.SL_RANGE.length * CONFIG.TP_RANGE.length;
    
    const startTime = Date.now();
    
    for (const sl of CONFIG.SL_RANGE) {
        for (const tp of CONFIG.TP_RANGE) {
            completedCombos++;
            const comboStartTime = Date.now();
            
            log(`🔄 Running SL${sl}_TP${tp} (${completedCombos}/${totalCombos})...`);
            
            const result = await runSingleCombo(sl, tp);
            allResults.push(result);
            
            const comboTime = ((Date.now() - comboStartTime) / 1000).toFixed(1);
            const avgTime = ((Date.now() - startTime) / completedCombos / 1000).toFixed(1);
            const remainingTime = ((totalCombos - completedCombos) * avgTime / 60).toFixed(1);
            
            log(`✅ SL${sl}_TP${tp}: ${result.totalTrades} trades, ${result.winRate.toFixed(1)}% win rate, $${result.totalPnL.toFixed(2)} PnL (${comboTime}s)`);
            log(`📊 Progress: ${completedCombos}/${totalCombos} (${(completedCombos/totalCombos*100).toFixed(1)}%) - ETA: ${remainingTime} minutes`);
            log('');
        }
    }
    
    // Sort results by total PnL
    allResults.sort((a, b) => b.totalPnL - a.totalPnL);
    
    // Save detailed results
    const detailedFile = path.join(CONFIG.OUT_DIR, `grid_results_detailed_${Date.now()}.json`);
    fs.writeFileSync(detailedFile, JSON.stringify(allResults, null, 2));
    
    // Generate summary report
    const summaryFile = path.join(CONFIG.OUT_DIR, `GRID_SUMMARY_REPORT.txt`);
    generateSummaryReport(allResults, summaryFile);
    
    log('🎉 COMPREHENSIVE GRID BACKTEST COMPLETE!');
    log(`📊 Results saved to: ${CONFIG.OUT_DIR}`);
    log(`📊 Detailed results: ${detailedFile}`);
    log(`📊 Summary report: ${summaryFile}`);
    
    // Display top 10 performers
    log('\n🏆 TOP 10 PERFORMERS BY TOTAL P&L:');
    allResults.slice(0, 10).forEach((result, index) => {
        log(`${index + 1}. SL${result.slPoints}_TP${result.tpPoints}: $${result.totalPnL.toFixed(2)} (${result.winRate.toFixed(1)}% win rate, ${result.totalTrades} trades)`);
    });
}

function generateSummaryReport(results, outputFile) {
    const report = `
============================================================================
COMPREHENSIVE GRID BACKTEST RESULTS SUMMARY
============================================================================
Generated: ${new Date().toISOString()}
Date Range: ${CONFIG.START_DATE} to ${CONFIG.END_DATE}
Trading Hours: 7am-3pm CST/CDT
Total Combinations: ${results.length}

SL Range: [${CONFIG.SL_RANGE.join(', ')}]
TP Range: [${CONFIG.TP_RANGE.join(', ')}]

============================================================================
TOP 20 PERFORMERS BY TOTAL P&L:
============================================================================

${results.slice(0, 20).map((r, i) => 
    `${(i+1).toString().padStart(2)}. SL${r.slPoints}_TP${r.tpPoints}: $${r.totalPnL.toFixed(2).padStart(10)} | ${r.winRate.toFixed(1).padStart(5)}% WR | ${r.totalTrades.toString().padStart(4)} trades | PF: ${r.profitFactor.toFixed(2)}`
).join('\n')}

============================================================================
STATISTICS:
============================================================================

Best Performer: SL${results[0].slPoints}_TP${results[0].tpPoints} ($${results[0].totalPnL.toFixed(2)})
Worst Performer: SL${results[results.length-1].slPoints}_TP${results[results.length-1].tpPoints} ($${results[results.length-1].totalPnL.toFixed(2)})

Average P&L: $${(results.reduce((sum, r) => sum + r.totalPnL, 0) / results.length).toFixed(2)}
Profitable Combos: ${results.filter(r => r.totalPnL > 0).length}/${results.length} (${(results.filter(r => r.totalPnL > 0).length / results.length * 100).toFixed(1)}%)

Average Trades: ${Math.round(results.reduce((sum, r) => sum + r.totalTrades, 0) / results.length)}
Average Win Rate: ${(results.reduce((sum, r) => sum + r.winRate, 0) / results.length).toFixed(1)}%

============================================================================
`;
    
    fs.writeFileSync(outputFile, report);
}

if (require.main === module) {
    main().catch(error => {
        log(`❌ Fatal error: ${error.message}`, 'ERROR');
        console.error(error);
        process.exit(1);
    });
}

module.exports = { CONFIG, runSingleCombo, main };
