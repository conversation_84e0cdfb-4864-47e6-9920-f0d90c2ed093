@echo off
REM ============================================================================
REM INDIVIDUAL COMBO BACKTESTING BATCH SCRIPT
REM ============================================================================
REM Runs each SL/TP combination separately with 1-second data validation
REM Your preferred combinations: SL6 with TP values 8, 10, 12, 15, 18, 25
REM ============================================================================

echo.
echo ============================================================================
echo INDIVIDUAL COMBO BACKTESTING - VALIDATED TRADING EDGE
echo ============================================================================
echo Testing SL6 with TP values: 8, 10, 12, 15, 18, 25
echo Using 1-second data validation for accurate SL/TP hits
echo Date Range: Jan 2020 - Aug 2025 (5+ years)
echo ============================================================================
echo.

REM Create results directory
if not exist "D:\backtest-output\individual_combo_results" mkdir "D:\backtest-output\individual_combo_results"

REM Set common environment variables
set CANDLES_DIR=D:\backtest-output\1min_candles_built_from_1sec
set OHLCV_1SEC_DIR=D:\backtest-output\1secdata_jan2020_aug2025\GLBX-20250830-HKWD79X9FR
set OUT_DIR=D:\backtest-output\individual_combo_results
set LOG_LEVEL=INFO

echo Starting individual combo backtests...
echo.

REM ============================================================================
REM SL6_TP8
REM ============================================================================
echo [%TIME%] Running SL6_TP8...
set SL_POINTS=6
set TP_POINTS=8
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP8 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP8 completed successfully
echo.

REM ============================================================================
REM SL6_TP10
REM ============================================================================
echo [%TIME%] Running SL6_TP10...
set SL_POINTS=6
set TP_POINTS=10
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP10 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP10 completed successfully
echo.

REM ============================================================================
REM SL6_TP12
REM ============================================================================
echo [%TIME%] Running SL6_TP12...
set SL_POINTS=6
set TP_POINTS=12
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP12 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP12 completed successfully
echo.

REM ============================================================================
REM SL6_TP15
REM ============================================================================
echo [%TIME%] Running SL6_TP15...
set SL_POINTS=6
set TP_POINTS=15
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP15 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP15 completed successfully
echo.

REM ============================================================================
REM SL6_TP18
REM ============================================================================
echo [%TIME%] Running SL6_TP18...
set SL_POINTS=6
set TP_POINTS=18
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP18 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP18 completed successfully
echo.

REM ============================================================================
REM SL6_TP25
REM ============================================================================
echo [%TIME%] Running SL6_TP25...
set SL_POINTS=6
set TP_POINTS=25
node individual_combo_backtester.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: SL6_TP25 failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)
echo [%TIME%] SL6_TP25 completed successfully
echo.

REM ============================================================================
REM COMPLETION SUMMARY
REM ============================================================================
echo ============================================================================
echo ALL COMBO BACKTESTS COMPLETED SUCCESSFULLY!
echo ============================================================================
echo.
echo Results saved to: D:\backtest-output\individual_combo_results\
echo.
echo Individual result files:
echo - SL6_TP8_results.json
echo - SL6_TP10_results.json
echo - SL6_TP12_results.json
echo - SL6_TP15_results.json
echo - SL6_TP18_results.json
echo - SL6_TP25_results.json
echo.
echo [%TIME%] All backtests completed at %DATE% %TIME%
echo.

REM Display summary of all results
echo ============================================================================
echo RESULTS SUMMARY:
echo ============================================================================
echo.

for %%f in ("D:\backtest-output\individual_combo_results\SL6_TP*_results.json") do (
    echo Processing %%~nf...
    REM Note: Would need PowerShell or additional script to parse JSON and display summary
)

echo.
echo To view detailed results, check the JSON files in:
echo D:\backtest-output\individual_combo_results\
echo.
echo Press any key to exit...
pause >nul
