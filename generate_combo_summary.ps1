# ============================================================================
# COMBO RESULTS SUMMARY GENERATOR
# ============================================================================
# Processes individual combo backtest results and generates comprehensive summary
# Reads JSON result files and creates formatted reports
# ============================================================================

param(
    [string]$ResultsDir = "D:\backtest-output\individual_combo_results",
    [string]$OutputFile = "D:\backtest-output\individual_combo_results\COMBO_SUMMARY_REPORT.txt"
)

Write-Host "============================================================================" -ForegroundColor Cyan
Write-Host "COMBO RESULTS SUMMARY GENERATOR" -ForegroundColor Cyan
Write-Host "============================================================================" -ForegroundColor Cyan
Write-Host ""

# Check if results directory exists
if (-not (Test-Path $ResultsDir)) {
    Write-Host "ERROR: Results directory not found: $ResultsDir" -ForegroundColor Red
    exit 1
}

# Get all result files
$resultFiles = Get-ChildItem -Path $ResultsDir -Filter "SL6_TP*_results.json" | Sort-Object Name

if ($resultFiles.Count -eq 0) {
    Write-Host "ERROR: No result files found in $ResultsDir" -ForegroundColor Red
    exit 1
}

Write-Host "Found $($resultFiles.Count) result files:" -ForegroundColor Green
foreach ($file in $resultFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor Yellow
}
Write-Host ""

# Initialize summary data
$summaryData = @()

# Process each result file
foreach ($file in $resultFiles) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Yellow
    
    try {
        $jsonContent = Get-Content -Path $file.FullName -Raw | ConvertFrom-Json
        
        $combo = [PSCustomObject]@{
            Combo = "SL$($jsonContent.slPoints)_TP$($jsonContent.tpPoints)"
            SL = $jsonContent.slPoints
            TP = $jsonContent.tpPoints
            TotalTrades = $jsonContent.totalTrades
            WinningTrades = $jsonContent.winningTrades
            LosingTrades = $jsonContent.losingTrades
            WinRate = [math]::Round($jsonContent.winRate, 1)
            TotalPnL = [math]::Round($jsonContent.totalPnL, 2)
            MaxDrawdown = [math]::Round($jsonContent.maxDrawdown, 2)
            AvgWin = [math]::Round($jsonContent.avgWin, 2)
            AvgLoss = [math]::Round($jsonContent.avgLoss, 2)
            ProfitFactor = [math]::Round($jsonContent.profitFactor, 2)
        }
        
        $summaryData += $combo
        Write-Host "  ✅ $($combo.Combo): $($combo.TotalTrades) trades, $($combo.WinRate)% win rate, $$$($combo.TotalPnL) PnL" -ForegroundColor Green
        
    } catch {
        Write-Host "  ❌ Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# Sort by total PnL descending
$summaryData = $summaryData | Sort-Object TotalPnL -Descending

# Generate summary report
$report = @"
============================================================================
INDIVIDUAL COMBO BACKTEST RESULTS SUMMARY
============================================================================
Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Date Range: January 2020 - August 2025 (5+ years)
Trading Hours: 7am-3pm CST/CDT
Position Size: 3 MNQ micro contracts ($6 per point)

✅ TRADING EDGE SPECIFICATION:
• 3-Candle Pattern: Green-Red-Green (bullish) / Red-Green-Red (bearish)
• MA Confluence: Price > WMA50 > WMA200 (bullish) / Price < WMA50 < WMA200 (bearish)  
• RSI Conditions: RSI > 60 (bullish) / RSI < 40 (bearish)
• SL/TP Validation: 1-second tick data for accurate entry/exit timing
• Slippage: Entry +0.25, SL +0.5, TP +0.0 points

============================================================================
RESULTS RANKED BY TOTAL P&L:
============================================================================

"@

# Add formatted table
$report += "`n"
$report += "Combo       Trades  Win%   Total P&L    Max DD    Avg Win   Avg Loss   P.Factor`n"
$report += "------------------------------------------------------------------------`n"

foreach ($combo in $summaryData) {
    $report += "{0,-10} {1,6} {2,5}% {3,10} {4,9} {5,9} {6,10} {7,8}`n" -f 
        $combo.Combo,
        $combo.TotalTrades,
        $combo.WinRate,
        "`$$($combo.TotalPnL)",
        "`$-$($combo.MaxDrawdown)",
        "`$$($combo.AvgWin)",
        "`$$($combo.AvgLoss)",
        $combo.ProfitFactor
}

$report += "`n"

# Add detailed analysis
$bestCombo = $summaryData | Sort-Object TotalPnL -Descending | Select-Object -First 1
$bestWinRate = $summaryData | Sort-Object WinRate -Descending | Select-Object -First 1
$bestProfitFactor = $summaryData | Sort-Object ProfitFactor -Descending | Select-Object -First 1
$lowestDrawdown = $summaryData | Sort-Object MaxDrawdown | Select-Object -First 1

$report += @"
============================================================================
KEY INSIGHTS:
============================================================================

🏆 BEST OVERALL PERFORMANCE:
   $($bestCombo.Combo): $$$($bestCombo.TotalPnL) total P&L, $($bestCombo.WinRate)% win rate

📈 HIGHEST WIN RATE:
   $($bestWinRate.Combo): $($bestWinRate.WinRate)% win rate, $$$($bestWinRate.TotalPnL) total P&L

⚖️ BEST PROFIT FACTOR:
   $($bestProfitFactor.Combo): $($bestProfitFactor.ProfitFactor) profit factor, $$$($bestProfitFactor.TotalPnL) total P&L

🛡️ LOWEST DRAWDOWN:
   $($lowestDrawdown.Combo): $$$($lowestDrawdown.MaxDrawdown) max drawdown, $$$($lowestDrawdown.TotalPnL) total P&L

============================================================================
TRADING STATISTICS SUMMARY:
============================================================================

Total Combinations Tested: $($summaryData.Count)
Average Trades per Combo: $([math]::Round(($summaryData | Measure-Object TotalTrades -Average).Average, 0))
Average Win Rate: $([math]::Round(($summaryData | Measure-Object WinRate -Average).Average, 1))%
Total P&L Range: $$$([math]::Round(($summaryData | Measure-Object TotalPnL -Minimum).Minimum, 2)) to $$$([math]::Round(($summaryData | Measure-Object TotalPnL -Maximum).Maximum, 2))

============================================================================
RECOMMENDATIONS:
============================================================================

Based on the 5+ year backtest results:

1. 🎯 PRIMARY RECOMMENDATION: $($bestCombo.Combo)
   - Highest total P&L: $$$($bestCombo.TotalPnL)
   - Win rate: $($bestCombo.WinRate)%
   - Max drawdown: $$$($bestCombo.MaxDrawdown)

2. 🎯 CONSERVATIVE OPTION: $($lowestDrawdown.Combo)
   - Lowest risk profile with $$$($lowestDrawdown.MaxDrawdown) max drawdown
   - Consistent performance: $($lowestDrawdown.WinRate)% win rate

3. 🎯 HIGH ACCURACY OPTION: $($bestWinRate.Combo)
   - Highest win rate: $($bestWinRate.WinRate)%
   - Reliable signal quality with good P&L

============================================================================
NEXT STEPS:
============================================================================

1. ✅ Review detailed trade logs in individual JSON files
2. ✅ Consider demo trading with top-performing combo
3. ✅ Implement risk management rules for live trading
4. ✅ Monitor performance in current market conditions

============================================================================
VALIDATION NOTES:
============================================================================

✅ All entries validated through 1-second tick data
✅ Realistic slippage applied to all trades
✅ Proper SL/TP priority (SL checked first)
✅ Trading hours strictly enforced (7am-3pm CST/CDT)
✅ Dynamic front-month contract rollover handled
✅ 5+ years of comprehensive market data tested

Report generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
============================================================================
"@

# Save report to file
try {
    $report | Out-File -FilePath $OutputFile -Encoding UTF8
    Write-Host "✅ Summary report saved to: $OutputFile" -ForegroundColor Green
} catch {
    Write-Host "❌ Error saving report: $($_.Exception.Message)" -ForegroundColor Red
}

# Display summary to console
Write-Host "============================================================================" -ForegroundColor Cyan
Write-Host "QUICK SUMMARY:" -ForegroundColor Cyan
Write-Host "============================================================================" -ForegroundColor Cyan

foreach ($combo in $summaryData) {
    $color = if ($combo.TotalPnL -gt 0) { "Green" } else { "Red" }
    Write-Host "$($combo.Combo): $($combo.TotalTrades) trades, $($combo.WinRate)% win rate, $$$($combo.TotalPnL) P&L" -ForegroundColor $color
}

Write-Host ""
Write-Host "🏆 Best Performer: $($bestCombo.Combo) with $$$($bestCombo.TotalPnL) total P&L" -ForegroundColor Yellow
Write-Host "📊 Full report saved to: $OutputFile" -ForegroundColor Yellow
Write-Host ""
