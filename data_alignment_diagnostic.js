// ============================================================================
// DATA ALIGNMENT DIAGNOSTIC TOOL
// ============================================================================
// Checks alignment between 1-minute candles and 1-second data
// Identifies contract mismatches, price gaps, and timing issues

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CONFIG = {
    CANDLES_DIR: 'D:\\backtest-output\\1min_candles_built_from_1sec',
    OHLCV_1SEC_DIR: 'D:\\backtest-output\\1secdata_jan2020_aug2025\\GLBX-20250830-HKWD79X9FR',
    SYMBOL: 'MNQ',
    TEST_DATE: '2024-08-30' // Use a recent date for testing
};

function formatDate(date) {
    return date.toISOString().split('T')[0].replace(/-/g, '');
}

function loadPrebuiltCandles(date) {
    const dateStr = formatDate(date);
    const candleFile = path.join(CONFIG.CANDLES_DIR, `${dateStr}_1min_candles.csv`);
    
    if (!fs.existsSync(candleFile)) {
        console.log(`❌ Candle file not found: ${candleFile}`);
        return [];
    }
    
    try {
        const csvData = fs.readFileSync(candleFile, 'utf8');
        const lines = csvData.trim().split('\n');
        const candles = [];
        
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            const parts = line.split(',');
            if (parts.length < 7) continue;
            
            const candle = {
                symbol: parts[0],
                timestamp: new Date(parts[1]),
                open: parseFloat(parts[2]),
                high: parseFloat(parts[3]),
                low: parseFloat(parts[4]),
                close: parseFloat(parts[5]),
                volume: parseInt(parts[6])
            };
            
            candles.push(candle);
        }
        
        return candles;
        
    } catch (error) {
        console.log(`❌ Error loading candles: ${error.message}`);
        return [];
    }
}

function load1SecondData(date) {
    const dateStr = formatDate(date);
    const fileName = `glbx-mdp3-${dateStr}.ohlcv-1s.csv.zst`;
    const filePath = path.join(CONFIG.OHLCV_1SEC_DIR, fileName);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ 1-second file not found: ${filePath}`);
        return [];
    }
    
    try {
        const csvData = execSync(`zstd -d -c "${filePath}"`, { 
            encoding: 'utf8',
            maxBuffer: 1024 * 1024 * 100 // 100MB buffer
        });
        const lines = csvData.trim().split('\n');
        const data = [];
        
        for (let i = 1; i < lines.length && i < 1000; i++) { // Limit to first 1000 for diagnostic
            const line = lines[i].trim();
            if (!line) continue;
            
            const parts = line.split(',');
            if (parts.length < 10) continue;
            
            const symbol = parts[9];
            if (!symbol.startsWith(CONFIG.SYMBOL) || symbol.includes('-')) continue;
            
            const tick = {
                symbol,
                timestamp: new Date(parts[0]),
                open: parseFloat(parts[4]),
                high: parseFloat(parts[5]),
                low: parseFloat(parts[6]),
                close: parseFloat(parts[7]),
                volume: parseInt(parts[8]) || 0
            };
            
            if (!isNaN(tick.open) && !isNaN(tick.high) && !isNaN(tick.low) && !isNaN(tick.close)) {
                data.push(tick);
            }
        }
        
        return data;
        
    } catch (error) {
        console.log(`❌ Error loading 1-second data: ${error.message}`);
        return [];
    }
}

function analyzePriceMovements(data) {
    console.log('\n🔍 ANALYZING 1-SECOND PRICE MOVEMENTS:');
    
    let maxMove = 0;
    let largeMovesCount = 0;
    let totalMoves = 0;
    
    for (let i = 1; i < data.length; i++) {
        const prev = data[i-1];
        const curr = data[i];
        
        const move = Math.abs(curr.close - prev.close);
        totalMoves++;
        
        if (move > maxMove) {
            maxMove = move;
        }
        
        if (move > 3) { // Moves larger than 3 points
            largeMovesCount++;
            console.log(`⚠️  Large move: ${prev.close} → ${curr.close} (${move.toFixed(2)} points) at ${curr.timestamp.toISOString()}`);
        }
    }
    
    console.log(`📊 Max 1-second move: ${maxMove.toFixed(2)} points`);
    console.log(`📊 Large moves (>3pts): ${largeMovesCount}/${totalMoves} (${(largeMovesCount/totalMoves*100).toFixed(1)}%)`);
    console.log(`📊 Average move: ${(data.reduce((sum, tick, i) => i > 0 ? sum + Math.abs(tick.close - data[i-1].close) : sum, 0) / (totalMoves || 1)).toFixed(2)} points`);
}

function compareDataSources(candles, oneSecData) {
    console.log('\n🔍 COMPARING DATA SOURCES:');
    
    if (candles.length === 0 || oneSecData.length === 0) {
        console.log('❌ Missing data for comparison');
        return;
    }
    
    // Find overlapping time period
    const candleStart = candles[0].timestamp;
    const candleEnd = candles[candles.length - 1].timestamp;
    const oneSecStart = oneSecData[0].timestamp;
    const oneSecEnd = oneSecData[oneSecData.length - 1].timestamp;
    
    console.log(`📊 1-min candles: ${candleStart.toISOString()} to ${candleEnd.toISOString()}`);
    console.log(`📊 1-sec data: ${oneSecStart.toISOString()} to ${oneSecEnd.toISOString()}`);
    
    // Check contract symbols
    const candleSymbols = [...new Set(candles.map(c => c.symbol))];
    const oneSecSymbols = [...new Set(oneSecData.map(d => d.symbol))];
    
    console.log(`📊 1-min candle symbols: ${candleSymbols.join(', ')}`);
    console.log(`📊 1-sec data symbols: ${oneSecSymbols.join(', ')}`);
    
    // Check price ranges
    const candlePrices = candles.flatMap(c => [c.open, c.high, c.low, c.close]);
    const oneSecPrices = oneSecData.flatMap(d => [d.open, d.high, d.low, d.close]);
    
    const candleMin = Math.min(...candlePrices);
    const candleMax = Math.max(...candlePrices);
    const oneSecMin = Math.min(...oneSecPrices);
    const oneSecMax = Math.max(...oneSecPrices);
    
    console.log(`📊 1-min price range: ${candleMin} - ${candleMax}`);
    console.log(`📊 1-sec price range: ${oneSecMin} - ${oneSecMax}`);
    
    // Check for alignment issues
    if (Math.abs(candleMin - oneSecMin) > 10 || Math.abs(candleMax - oneSecMax) > 10) {
        console.log('🚨 PRICE RANGE MISMATCH DETECTED!');
    }
    
    if (candleSymbols[0] !== oneSecSymbols[0]) {
        console.log('🚨 CONTRACT SYMBOL MISMATCH DETECTED!');
    }
}

function main() {
    console.log('🔍 DATA ALIGNMENT DIAGNOSTIC STARTING...');
    console.log(`📊 Test Date: ${CONFIG.TEST_DATE}`);
    
    const testDate = new Date(CONFIG.TEST_DATE);
    
    // Load both data sources
    console.log('\n📥 Loading 1-minute candles...');
    const candles = loadPrebuiltCandles(testDate);
    console.log(`✅ Loaded ${candles.length} 1-minute candles`);
    
    console.log('\n📥 Loading 1-second data...');
    const oneSecData = load1SecondData(testDate);
    console.log(`✅ Loaded ${oneSecData.length} 1-second ticks`);
    
    if (candles.length > 0) {
        console.log(`📊 First candle: ${candles[0].symbol} at ${candles[0].timestamp.toISOString()} - OHLC: ${candles[0].open}/${candles[0].high}/${candles[0].low}/${candles[0].close}`);
        console.log(`📊 Last candle: ${candles[candles.length-1].symbol} at ${candles[candles.length-1].timestamp.toISOString()} - OHLC: ${candles[candles.length-1].open}/${candles[candles.length-1].high}/${candles[candles.length-1].low}/${candles[candles.length-1].close}`);
    }
    
    if (oneSecData.length > 0) {
        console.log(`📊 First 1-sec: ${oneSecData[0].symbol} at ${oneSecData[0].timestamp.toISOString()} - OHLC: ${oneSecData[0].open}/${oneSecData[0].high}/${oneSecData[0].low}/${oneSecData[0].close}`);
        console.log(`📊 Last 1-sec: ${oneSecData[oneSecData.length-1].symbol} at ${oneSecData[oneSecData.length-1].timestamp.toISOString()} - OHLC: ${oneSecData[oneSecData.length-1].open}/${oneSecData[oneSecData.length-1].high}/${oneSecData[oneSecData.length-1].low}/${oneSecData[oneSecData.length-1].close}`);
    }
    
    // Analyze price movements
    if (oneSecData.length > 1) {
        analyzePriceMovements(oneSecData);
    }
    
    // Compare data sources
    compareDataSources(candles, oneSecData);
    
    console.log('\n🎯 DIAGNOSTIC COMPLETE!');
}

if (require.main === module) {
    main();
}
